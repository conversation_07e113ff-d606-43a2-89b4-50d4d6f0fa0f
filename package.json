{"name": "keyon-blog", "title": "<PERSON>on Blog", "author": "Keyon <<EMAIL>>", "version": "1.7.0", "homepage": "http://KeanChen.github.io", "repository": {"type": "git", "url": "https://github.com/KeanChen/KeanChen.github.io"}, "bugs": "https://github.com/KeanChen/KeanChen.github.io/issues", "devDependencies": {"grunt": "~0.4.5", "grunt-contrib-less": "~0.11.4", "grunt-contrib-watch": "~0.6.1", "grunt-banner": "~0.2.3", "grunt-contrib-uglify": "~0.5.1"}, "scripts": {"preview": "cd _site; python -m SimpleHTTPServer 8020", "py3view": "cd _site; python3 -m http.server 8020", "watch": "grunt watch & npm run preview & jekyll serve -w", "py3wa": "grunt watch & npm run py3view & jekyll serve -w", "boil": "git push boilerplate boilerplate:master", "push": "git push origin master --tag", "cafe": "git co gitcafe-pages; git merge master; git push gitcafe gitcafe-pages:gitcafe-pages --tag; git co master;"}}