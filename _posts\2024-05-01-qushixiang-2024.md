---
layout:     post                    # 使用的布局（不需要改）
title:      怎样用好最重要的几年？              # 标题 
subtitle:   随机游走中趋势项 #副标题
date:       2024-05-01              # 时间
author:     Keyon                      # 作者
catalog: true                       # 是否归档
tags:
  Code
---

去年看了徐高在国发院的演讲稿[《怎样用好最重要的几年？》](https://www.nsd.pku.edu.cn/jxxm/yjs/xsgz/xshd/263375.htm)，很受启发。

我对演讲中的**随机游走中趋势项**做了计算机的模拟，并参考了MIT的[6.0002](https://www.youtube.com/watch?v=6wUD_gp5WeE)，源码如下：

```
from random import choice
import matplotlib.pyplot as plt

class RandomWalk():
    """A class to generate random walks."""
    
    def __init__(self, num_points=5000):
        """Initialize attributes of a walk."""
        self.num_points = num_points
        
        # All walks start at (0, 0).
        self.x_values = [0]
        self.y_values = [0]

    def fill_walk(self):
        """Calculate all the points in the walk."""
        
        # Keep taking steps until the walk reaches the desired length.
        while len(self.x_values) < self.num_points:
            
            # Decide which direction to go, and how far to go in that direction.
            x_direction = choice([1, -1])
            x_distance = choice([0, 1])
            x_step = x_direction * x_distance
            
            y_direction = choice([1, -1])
            y_distance = choice([0, 1])
            y_step = y_direction * y_distance
            
            # Reject moves that go nowhere.
            if x_step == 0 and y_step == 0:
                continue
            
            # Calculate the next x and y values.
            next_x = self.x_values[-1] + x_step + 0.01
            next_y = self.y_values[-1] + y_step + 0.01
            
            self.x_values.append(next_x)
            self.y_values.append(next_y)

rw = RandomWalk(100000)
rw.fill_walk()

plt.style.use("dark_background")
fig, ax = plt.subplots(figsize=(15, 9), dpi = 128)
point_numbers = range(rw.num_points)
ax.scatter(rw.x_values, rw.y_values,c = point_numbers,cmap = plt.cm.Blues, 
           edgecolors = 'none', s = 5)

ax.scatter(0, 0, c = 'green', edgecolors = 'none', s = 100)
ax.scatter(rw.x_values[-1], rw.x_values[-1], c = 'red', edgecolors = 'none', 
           s = 100)

ax.get_xaxis().set_visible(False)
ax.get_yaxis().set_visible(False)
    
plt.show()
```