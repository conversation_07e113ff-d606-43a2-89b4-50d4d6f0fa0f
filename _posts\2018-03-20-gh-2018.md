---
layout:     post                    # 使用的布局（不需要改）
title:      为什么你听过这么多道理却过不好这一生              # 标题 
subtitle:   问题在于“听” #副标题
date:       2018-03-20              # 时间
author:     Keyon                      # 作者
catalog: true                       # 是否归档
tags:
 元学习
---

指望听道理从而过好这一生的人，他的隐含逻辑是先听道理，然后获得一些知识，通过这些知识获利，从而过好这一生。

如果我们把这个环环相扣的逻辑链条拆开看，我们就能发现它的漏洞在哪了。

首先，通过知识获利是完全可行的，查理·芒格就是靠自己的聪明才智获得的巨额收益，如果你觉得这个是幸存者偏差，那么你看看那些其他的既得利益者，他们基本上都是很聪明的人，保持聪明的方法就是不断学习。

那么问题最可能出现的地方就在听道理获取知识了。要想分析这个获取知识的方式有什么问题，我们首先弄清楚这两个问题：

* 我们学习的是什么知识？
* 我们学习的方式是什么？

## 两种知识
我把知识分为外显知识和内隐知识。下面我会详细介绍这两个概念的定义，以及用法。

外显知识容易描述，可以被文字、音频等形式承载，从而容易被传递。

得到外显知识的途径是**输入**，最常见的就是看书、看视频。我们阅读一篇菜谱，然后按照菜谱的指导做出一道菜，这就是外显知识被传递并起作用的结果。

我们从小到大都在被教导如何学好外显知识，每一个中国人在学生时代面对最多的就是课本和老师的教导，然后用从这些途径学到的知识解决卷面难题，这种学习过程就是学会外显知识并应用的过程。

市面上各种有关如何学习的教程基本上都是在教你如何学好外显知识的，然而对学好内隐知识的材料却太少，下面我会大篇幅描述学习内隐知识的过程。

内隐知识存在于人的头脑中，难以用语言和其他形式载体描述，所以它们不存在于书本、视频里，这些造成了它们难以传递的特性。

如果你问林丹如何打好羽毛球，他可能会很慷慨的告诉你很多技巧，但是知道了这些并不能让你第二个林丹，甚至不能让你立刻打好羽毛球。

原因就在于打羽毛球技术动作都是由大量练习的内隐知识构成的，而你只是听了一些关于羽毛球的练习方法，没有切身**体会**任何击球动作，大脑没有建立有关打羽毛球的神经元链接。没有刻意练习的过程，也就得不到这些内隐知识。

那么如何学习内隐知识呢？答案就是**行动**，用行动浇筑内隐知识。光凭看书不可能学会游泳，除非你下水尝试；不上路的话，你永远学不会开车。

知道为什么有人听过那么多道理却过不好这一生了吧。他们的学习方式只限于输入，这样的方法能帮助他们学会外显知识，却不能帮助他们建立内隐知识的体系，然而往往能帮助他们改变命运的就是那些关键的内隐知识。

好了，我们已经知道内隐知识要用行动构建了，可有什么方法可以加速学习过程呢？

答案就在被工业社会抛弃的制度上，就是**师徒制**。

工业社会采用精细化分工的方式，而这种工作方式需要大量的受过专业化训练的人，把他们放到工业大机器里承担小螺丝钉的作用。要满足这种人才需求，**批量处理**成为了培养人才的原则，即一对多的培养模式。

于是，师徒制这种一对一的模式被认为是降低效率的，渐渐就被抛弃了。

其实师徒制自有其可贵之处。为什么运动员需要教练而不是一对书籍和教学视频？原因在于教练可以看出运动员的技术失误，这种技术失误往往不是单点问题而是系统问题。

就拿乒乓球来说，挥拍动作的问题不可能仅仅出现在手肘上，可能你整个身体的发力顺序就不对，腰没转动，腿没蹬地，都会造成挥拍动作问题。

而这时你叫运动员对着视频练，他发现错误的效率会大大降低，甚至错上加错；也不可能有一本书告诉他，遇到这种错误应该怎么办，因为这个错误太系统太复杂，不是一本书能描述的，即使能描述，效率也太低。

这时如果有教练在，一切都好办了：教练演示一下错误动作，再演示一下改进动作，就OK了。

这个演示的过程就是内隐知识传递的过程，它不是靠说教传递的，而是靠行动传递的。演示包含的信息丰富，而且运动员一眼就能看明白，立马就能用到改进中，当他改进了自己的错误动作，他也就学会了这个内隐知识。

> 不光光运动领域的学习模式是这样，其他没有固定成功模式的领域也是如此。比如投资，影响投资的变量太多太多，我们无法通过阅读掌握这项技能，最好的学习投资的方式就是去投资。
> 
> 你无法靠看巴菲特的书籍、演讲、访谈而成为第二个巴菲特，能成就巴菲特的内隐知识无法靠这些途径传递，比较靠谱的方式是成为他的学徒，但这也不是一般人能有机会的。

当然并不是有了师傅指导就可以一劳永逸了，中国有句老话“师傅领进门，修行在个人”，我们自己也要做出相应的努力。

我们要做的就是**“尽可能全面的模仿”**，注意前面的修辞，不是单单的“模仿”，而是“尽可能全面的模仿”。

你可能会说，我只需要学习他好的方面就可以了，为什么要尽可能全面？难道他不好的方面也要学吗？

你的师傅能有今天的成就，肯定不单单靠的是你认为的优点达成的，有些你认为的缺点，有可能就是他有所成就的关键。比如你觉得暴躁是他的缺点，但是有可能这样的脾气让他的合作方觉得这个人有话直说，沟通成本低，是可贵的品质。

所以，不要以你的角度评判好坏，你在开始可能会迷惑，不理解为什么。我的策略是先照做，慢慢在行动中思考为什么，而不是先思考为什么再行动，那时候一切都晚了。

你还有可能会问，万一他坑我怎么办？

他坑你的概率不大，你们本身就不是一个等级的，他坑了你收益太低，根本不值得。再者有贵人相助是一件幸事，太过严重的被迫害妄想症会让你错失很多机会。

## 两种学习方式
MIT有一个MOOC叫u.lab，讲师讲过这么一句话：“学习有截然不同的两种源头，一种是基于过去经验的学习，而另外一种是通过感知与实现正在生成的可能性。”

我们日常的学习基本上都是向过去的经验学习。大学课堂里教的知识都是人类文明的积累，数学、经济学、社会学、心理学等等学科都是前人的经验成果。

这种经验积累有利于人类的**集体学习**，对于推动人类文明的进步有着巨大的意义。通过文字的传承，我们能学习到前人的经验，从而不断改进这些知识，如果没有积累，我们只能每一代创造新的东西，然后被遗忘，周而复始，始终停留在文明的初始阶段。

就好比造飞机，它不是一代人研究出来的，而是好几代人经验累积的结果。前人把积累下来的经验留下，供后人学习后继续改进。我们今天看到的飞机，复杂度相当高，不可能靠一个人从无到有造出来。

对于我们个人，学习过去的经验也是至关重要的，学习前人留下的文化瑰宝，不仅仅是传承这么简单，我们要用它在未来创造更多的价值。

关于过去的经验，我们要学什么？答案是**“通识”**。

数学、经济学、社会学、政治学、心理学、计算机科学等等学科，都是被前人广泛验证的，且非常实用的学科，这些就是我们进行通识教育要学习的内容。

通识影响我们生活的方方面面，小到日常的开支计算，大到人生决策问题，我们都可以从通识中找到思考的原型。

比如你深刻认识到经济学**成本**的概念，你就会知道做任何事情都有成本，你进行选择的时候考量的就会越理性。

那么什么是“通过感知与实现正在生成的可能性”呢？你可以把它理解成一种创新的能力。

一切推动人类认知边界的创新都可以归于此类。比如博士的研究项目，新科技的发明，这些创新都是前人经验中所没有，由当代人之手创造出来的。

当然创新不只是这些高大上的科技创新，一个厨子创造了一道前所未有的新菜，这是创新；一个人总结了一个提升效率的新想法，这也是创新。

对于整个社会来说，这种创新能力是促使进步的源泉，是推动人类活动产物复杂度提升的原动力；对于个人来说，这种创新是增长财富、创造价值的方式。

## 怎样过好这一生
如果我们把以上讨论两两结合一下，做成一个矩阵，如何过好这一生的答案也就神奇地显现出来了。

![study](https://ws4.sinaimg.cn/large/006tKfTcgy1fpiklb6zq3j30pp0j5t95.jpg)

我们分别讲解这四块矩阵的含义。

**学习过去经验的外显知识**：提供谈资。

这种学习模式就是听道理获取知识的模式，靠这种方式学习的人通过各种途径输入外显知识，却没有把知识内化。这就是那些什么都知道一点，但什么都不精通的人，他们口若悬河，好像百科全书一样什么都懂，这往往会让别人觉得他们很厉害。

但是他们仅仅知道的外显知识只能够给他们提供这些让人崇拜的谈资而已。光了解知识远远不够，应用知识才能算是掌握，那些通过学习获利的人基本上都是通过掌握知识获得改变物理世界的力量，而不是靠了解什么东西就变得很牛逼。大多数人过不好这一生，根本原因在于没有想到这一点。

**学习感知未来的外显知识**：售卖。

这里不讨论那些新瓶装旧酒的卖课模式，虽然市场上占大部分的都是以这种方式盈利的。

当一个人通过感知未来，也就是通过创新获得了外显知识的时候，他就可以通过分享出去，扩散它的价值。

如果一个人发明了一种提高工作效率的工具，他卖给了其他人，创新者本身就可以获得一笔可观的收入，其他购买者也可以通过提升工作效率获得财富上的增长，这是一种极其有价值的双赢模式。

**学习过去经验的内隐知识**：跨学科解决问题。

我们通过大量学习和实践过去的经验获得内隐知识，它们在我们的头脑中**组块化**，当我们需要解决问题时，调用这些不同的组块灵活解决问题的过程就是跨学科解决问题。

《未来简史》中就有用数据解决经济学问题的例子，当大数据技术用于经济学分析时，技术提供的准确数据可以更好的帮助经济学修改它的模型。

学科与学科之间并非泾渭分明，一个领域的经验知识也可以很漂亮的解决其他领域的问题。要想以这种方式解决问题，**举一反三**的能力是必不可少的。

**学习感知未来的内隐知识**：形成认知壁垒。

有人说如果有时光机让10年前的我看到10年后是什么样，那么我肯定买比特币。

而有人没有时光机，却也长期持有比特币，最终获得巨额收益。

造成两种截然不同结果的原因在于这两种人所持信念不同。后者相信比特币有巨大价值，所以即使短期有小波动，也不会影响他们长期持有币的决心；而那些信念不坚定的人，看到一点小波动心态就爆炸，也就很难长期持有了。

这种信念不能从过去的经验获得，没人也没有一本书会告诉你关于比特币的一切，你只能用感知未来摸索它究竟是什么；这种信念也很难表达和传递，如果这信念那么容易被接受，那么最终比特币获利者将会是大多数人。

所以这种信念就是通过感知未来获得的内隐知识，它就是大多数人无法轻易逾越的**知识壁垒**。当你感知到未来的某种趋势的时候，你会在这个领域提前积累，待到它真的成为风口，其他人如墙头草跟风跑的时候，你会比他们更有优势，从中获利的概率也就更大。

综上，听过这么多道理却过不好这一生的原因也就明了了，听道理仅仅占了这个矩阵的左上一部分，获得的只是谈资而已。要过好这一生，认知仅限于左上这一小部分是远远不够的。

对照一下这个矩阵，反思一下自己的学习还有哪些是需要加强的。这个矩阵不可能包括有关学习的全部，但也许能为你提供一个反思视角，带给你一些新的认识，帮你构建更好的生活。
