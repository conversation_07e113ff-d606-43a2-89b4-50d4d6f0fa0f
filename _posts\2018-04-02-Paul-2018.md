---
layout:     post                    # 使用的布局（不需要改）
title:      《黑客与画家》书评              # 标题 
subtitle:   路还很长 #副标题
date:       2018-04-02              # 时间
author:     Keyon                      # 作者
catalog: true                       # 是否归档
tags:
 书籍
---

> 封面是Paul Graham本人。

## 黑客与画家
在3月的尾巴看完了《黑客与画家》，实在觉得惊艳，这本书是Y Combinator创始人Paul Graham的文集，翻译者阮一峰的博客我之前也推荐过。

我强烈推荐你去读本书的第三章和第六章，“不能说的话”和“如何创造财富”。

第三篇可当作天朝生存指南，有些人可能觉得现在言论自由，没什么不能说的。

其实，要想证明不能说的话真的存在，举个例子就可以了。

众所周知，在中国是不能上Google的，因为谷歌崇尚信息自由，这与中国的要求有冲突，也就是说。谷歌搜索里的一些信息在中国是需要屏蔽掉的，这些被屏蔽掉的就是我们不能说的话。

我们可能会觉得哥白尼那个时代是荒谬的，日心说在我们现在看来就是比地心说要合理很多，那个时代就不承认这个事实，现在的我们比那个时代的人活得更贴近真实，信息更加透明。

反过来一想，我们现在没有不透明的地方吗？其实还是有的，我相信1000年后的人们回顾现在的历史，一定也会觉得我们活在一个比较愚昧的时代。不过你也不必颓唐，这意味着时代的进步。

既然有不能说的话，那么了解他们并避开他们，一定是对我们有益的，至于如何发现和他对付此类话的方式，不如认真看看这篇文章。

第六篇可以打破你对于财富的原始认知，他指出，除去赌博、投机、婚姻、继承、偷窃、敲诈、诈骗、垄断、行贿等方式致富，创业也许是获得财富最快的方法。

**创业的本质就是通过创造有价值的东西在市场上得到回报。**即便不是创业，你正常上班工作，你工作也是为公司创造价值，而公司拿着你和其他员工创造的价值放在市场上获得回报，这些回报的一部分就会变成你的公司。

你创造的价值就是财富，这和金钱还是有区别的。在你努力工作有价值产出时，你的产出并未兑换成金钱，但你已经创造出了财富。金钱是一般等价物，是财富的一种表现形式。

懂得了财富的本质，我们所需的就是提升创造财富的效率，创业即是创造财富效率最高的一种。Paul形容创业就是把工作时间压缩了，把四十年的工作压缩成了四年。

而且压缩的回报并不是仅仅在这四年里给你四十年的工作报酬，而是有额外加成的。在高技术领域，这种压缩的回报尤其丰厚，工作效率越高，额外报酬就越高。

值得一提的还有财富创造的**可测量性**和**放大性**。

**小团体作战可以体现可测量性。**因为小，你不仅可以挑选优秀的人进入这个小团体，还可以你可以精确测量每一个人的贡献多少，从而精准地奖惩，达到有效激励。

**高科技可以体现放大性。**我对放大性的理解大致如下，如果你创造了某样产品，它的价值取决于有多少人使用它，高科技总是趋于被普及的，所以在普及的过程中，使用人数自然也在增加，你产品的价值就在被放大。

你不读这一章也不会影响你创造财富，就像乒乓球运动员即使没学过空气动力学但也不妨碍他成为乒乓球高手，但理解这些原理，有助于帮助你取得成功。

此外，本书的文章编排顺序非常合理，把最有趣的“书呆子为什么不受欢迎”放在了第一篇，把关于编程语言的章节放在了最后面。这一篇文章可能会让你好奇：为什么这个硅谷创业教父会想这些？他还会想些哪些好玩的？这会激发你对这本书的兴趣，驱使你继续读下去。

值得关注的还有作者的文笔。半本书都是技术相关的话题，可是一点都没有那种拒外行人于千里之外的感觉。相反可读性相当高，尤其是那篇“防止垃圾邮件的一种方法”，写得特生动，可见作者一流的叙事能力。[^1]

[^1]: 在这里我不禁感到自愧不如，看来我的写作之路还很长。

这本书给我最大的启发就是要**经常思考**。即便是身边的小事也有值得思考的地方，若能养成思考的习惯，一是可以锻炼思维，二是使你在各个方面看问题比别人要更加深入和仔细。我相信这也是作者写“书呆子为什么不受欢迎”这种看似与创业、计算机八竿子打不着的话题的原因。