@import "variables.less";
@import "mixins.less";
@import "sidebar.less";
@import "side-catalog.less";

// Global Components

body {
	.sans-serif;
	font-size: 16px;
	// Hux mpdify to 16px (Mobile First), and increase to 20px while 768+ width
	color: @gray-dark;
	//-webkit-user-select:text; //对于 Blog 还是先不开启这句。
	overflow-x : hidden;
	/* 隐藏页面滚动条但保持滚动功能 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE and Edge */
	&::-webkit-scrollbar {
		display: none; /* Chrome, Safari, Opera */
	}
}

html {
	/* 隐藏html滚动条但保持滚动功能 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE and Edge */
	&::-webkit-scrollbar {
		display: none; /* Chrome, Safari, Opera */
	}
}

/* 全局隐藏所有滚动条 */
*::-webkit-scrollbar {
	display: none; /* Chrome, Safari, Opera */
}
* {
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE and Edge */
}

// -- Typography

p {
	margin: 30px 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	.sans-serif;
	line-height: 1.1;
	font-weight: bold;
}
h1{
	// drop V1.1.1
	//font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
h4{
    font-size: 21px;
}
a {
	color: @gray-dark;
	&:hover,
	&:focus {
		color: @brand-primary;
	}
}

a img {
	&:hover,
	&:focus {
		cursor: zoom-in;
	}
}

article{
	overflow-x : hidden;
}
blockquote {
	color: @gray;
	font-style: italic;
	font-size: 0.95em;
	margin: 20px 0 20px;
	p{
		margin: 0;
	}
}

// Utils Style Class can be used in Markdown.
small.img-hint{
	display: block;
	margin-top: -20px;
	text-align: center;
}
br + small.img-hint{
	margin-top: -40px;
}
img.shadow{
	box-shadow: rgba(0, 0, 0, 0.258824) 0px 2px 5px 0px;
}
// Utils Style End

// Select
select{
	-webkit-appearance:none;
	margin-top:15px;
	color:#337ab7;
	border-color:#337ab7;
	padding: 0em 0.4em;
	background: white;
	&.sel-lang{
		min-height: 28px;
		font-size:14px;
	}
}


// override table style in bootstrap
.table th, .table td{
	border: 1px solid #eee !important;
}

hr.small {
	max-width: 100px;
	margin: 15px auto;
	border-width: 4px;
	border-color: white;
}
// add by Hux
pre,.table-responsive{
	// sometimes you should use vendor-feature.
	-webkit-overflow-scrolling: touch;
}
pre code{
	display: block;
	width: auto;
	white-space: pre;	// save it but no-wrap;
	word-wrap: normal;	// no-wrap
}

// In the list of posts
.postlist-container{
	margin-bottom: 15px;
}

// In the post.
.post-container{
	a{
		color:#337ab7;
		// different to @brand-primary
		&:hover,
		&:focus {
			color: @brand-primary;
		}
	}
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		margin: 30px 0 10px;
	}
	h5{
		font-size: 19px;
		font-weight: 600;
		color:gray;
		& + p{
			margin-top: 5px;
		}
	}
	h6{
		font-size: 16px;
		font-weight: 600;
		color: gray;
		& + p{
			margin-top: 5px;
		}
	}
	ul,ol{
		margin-bottom: 40px;
		@media screen and (max-width: 768px){
			&{
				padding-left: 30px;
			}
		}
		@media screen and (max-width: 500px){
			&{
				padding-left: 20px;
			}
		}
	}
	ol ol, ol ul, ul ol, ul ul{
		margin-bottom: 5px
	}
	li{
		p{
			margin: 0;
			margin-bottom: 5px;
		}
		h1,h2,h3,h4,h5,h6{
			line-height: 2;
			margin-top: 20px;
		}
	}
	// V1.6 Hux display title by default.
	.pager li{
		width: 48%;
		&.next{ float: right};
		&.previous{ float: left};
		> a {
			width: 100%;
			> span{
				color: @gray;
				font-weight: normal;
				letter-spacing: 0.5px;
			}
		}
	}
}



// Navigation

// materialize, mobile only
@media only screen and (max-width: 767px) {
	/**
	 * Layout
	 * Since V1.6 we use absolute positioning to prevent to expand container-fluid
	 * which would cover tags. A absolute positioning make a new layer.
	 */
	.navbar-default .navbar-collapse{
		position: absolute;
     	right: 0;
		border: none;
		background: white;
		box-shadow: 0px 5px 10px 2px rgba(0, 0, 0, 0.2);
		box-shadow: rgba(0, 0, 0, 0.117647) 0px 1px 6px, rgba(0, 0, 0, 0.239216) 0px 1px 4px;
		border-radius: 2px;
		width: 170px;
	}
	/**
	 * Animation
	 * HuxBlog-Navbar using genuine Material Design Animation
	 */
	#huxblog_navbar{
		/**
		 * Sharable code and 'out' function
		 */
		// the container do width-transition
		opacity: 0;
		transform: scaleX(0);
		transform-origin: top right;
		transition: all 200ms cubic-bezier(0.47, 0, 0.4, 0.99) 0ms;
		-webkit-transform: scaleX(0);
		-webkit-transform-origin: top right;
		-webkit-transition: all 200ms cubic-bezier(0.47, 0, 0.4, 0.99) 0ms;
		a {
			font-size: 13px;
			line-height: 28px;
		}
		.navbar-collapse{	// the navbar do height-transition
			height: 0px; 	// to solve container-mask-tags issue (default state).
			transform: scaleY(0);
			transform-origin: top right;
			transition: transform 400ms cubic-bezier(0.32, 1, 0.23, 1) 0ms;
			-webkit-transform: scaleY(0);
			-webkit-transform-origin: top right;
			-webkit-transition: -webkit-transform 400ms cubic-bezier(0.32, 1, 0.23, 1) 0ms;
		}
		li{
			opacity: 0;
			transition: opacity 100ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
			-webkit-transition: opacity 100ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
		}
		/**
		 *'In' Animation
		 */
		&.in {
			transform: scaleX(1);
			-webkit-transform: scaleX(1);
			opacity: 1;
			transition: all 250ms cubic-bezier(0.23,1,0.32,1) 0ms;
			-webkit-transition: all 250ms cubic-bezier(0.23,1,0.32,1) 0ms;
			.navbar-collapse{
				transform: scaleY(1);
				-webkit-transform: scaleY(1);
				transition: transform 500ms cubic-bezier(0.23, 1, 0.32, 1);
				-webkit-transition: -webkit-transform 500ms cubic-bezier(0.23, 1, 0.32, 1);
			}
			li{
				opacity: 1;
				transition: opacity 450ms cubic-bezier(0.23, 1, 0.32, 1) 205ms;
				-webkit-transition: opacity 450ms cubic-bezier(0.23, 1, 0.32, 1) 205ms;
			}
		}
	}
}

.navbar-custom {
	// materialize
	background: none;
	border:none;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 3;
	.sans-serif;
	.navbar-brand {
		font-weight: 800;
		// materialize
		color: white;
		height: 56px;
		line-height: 25px;
		&:hover{
			color: rgba(255, 255, 255, 0.8);
		}
	}
	.nav {
		li {
			a {
				text-transform: uppercase;
				font-size: 12px;
				line-height: 20px;
				font-weight: 800;
				letter-spacing: 1px;
				// only highlight in mobile
				&:active{
					background: rgba(0,0,0,0.12)
				}
			}
		}
	}
	@media only screen and (min-width: 768px) {
		body{
			font-size: 20px;
		}
		background: transparent;
		border-bottom: 1px solid transparent;
		.navbar-brand {
			color: white;
			padding: 20px;
			line-height: 20px;
			&:hover,
			&:focus {
				color: @white-faded;
			}
		}
		.nav {
			li {
				a {
					color: white;
					padding: 20px;
					&:hover,
					&:focus {
						color: @white-faded;
					}
					&:active{
						background: none;
					}
				}
			}
		}
	}
	@media only screen and (min-width: 1170px) {
		-webkit-transition: background-color 0.3s;
		-moz-transition: background-color 0.3s;
		transition: background-color 0.3s;
		/* Force Hardware Acceleration in WebKit */
		-webkit-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		-o-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
		-webkit-backface-visibility: hidden;
		backface-visibility: hidden;
		&.is-fixed {
			/* when the user scrolls down, we hide the header right above the viewport */
			position: fixed;
			top: -61px;
			background-color: fade(white, 90%);
			border-bottom: 1px solid darken(white, 5%);
			-webkit-transition: -webkit-transform 0.3s;
			-moz-transition: -moz-transform 0.3s;
			transition: transform 0.3s;
			.navbar-brand {
				color: @gray-dark;
				&:hover,
				&:focus {
					color: @brand-primary;
				}
			}
			.nav {
				li {
					a {
						color: @gray-dark;
						&:hover,
						&:focus {
							color: @brand-primary;
						}
					}
				}
			}
		}
		&.is-visible {
			/* if the user changes the scrolling direction, we show the header */
			-webkit-transform: translate3d(0, 100%, 0);
			-moz-transform: translate3d(0, 100%, 0);
			-ms-transform: translate3d(0, 100%, 0);
			-o-transform: translate3d(0, 100%, 0);
			transform: translate3d(0, 100%, 0);
		}
	}
}

// Header

.intro-header {
	background: no-repeat center center;
	background-color: @gray;
	background-attachment: scroll;
	.background-cover;
	// NOTE: Background images are set within the HTML using inline CSS!
	margin-bottom: 0px;  /* 0 on mobile, modify by Hux */
	@media only screen and (min-width: 768px) {
		margin-bottom: 20px;  /* response on desktop */
	}
	.site-heading,
	.post-heading,
	.page-heading {
		padding: 85px 0 55px;
		color: white;
		@media only screen and (min-width: 768px) {
			padding: 150px 0;
		}
	}
	// masterialize
	.site-heading{
		padding: 95px 0 70px;
		@media only screen and (min-width: 768px) {
			padding: 150px 0;
		}
	}
	.site-heading,
	.page-heading {
		text-align: center;
		h1 {
			margin-top: 0;
			font-size: 50px;
		}
		.subheading {
			.sans-serif;
			font-size: 18px;
			line-height: 1.1;
			display: block;
			font-weight: 300;
			margin: 10px 0 0;
		}
		@media only screen and (min-width: 768px) {
			h1 {
				font-size: 80px;
			}
		}
	}
	.post-heading {
		h1 {
			font-size: 30px;
			margin-bottom: 24px;
		}
		.subheading,
		.meta {
			line-height: 1.1;
			display: block;
		}
		.subheading {
			.sans-serif;
			font-size: 17px;
			line-height: 1.4;
			font-weight: normal;
			margin: 10px 0 30px;
			margin-top: -5px;
		}
		.meta {
			.serif;
			font-style: italic;
			font-weight: 300;
			font-size: 18px;
			a {
				color: white;
			}
		}
		@media only screen and (min-width: 768px) {
			h1 {
				font-size: 55px;
			}
			.subheading {
				font-size: 30px;
			}
			.meta {
				font-size: 20px;
			}
		}
	}
}

// Post Preview Pages (Home Page)

.post-preview {
	> a {
		color: @gray-dark;
		&:hover,
		&:focus {
			text-decoration: none;
			color: @brand-primary;
		}
		> .post-title {
			font-size: 21px;
			line-height: 1.3;
			margin-top: 30px;
			margin-bottom: 8px;
		}
		> .post-subtitle {
			font-size: 15px;
			line-height: 1.3;
			margin: 0;
			font-weight: 300;
			margin-bottom: 10px;
		}
	}
	> .post-meta {
		.serif;
		color: @gray;
		font-size: 16px;
		font-style: italic;
		margin-top: 0;
		> a {
			text-decoration: none;
			color: @gray-dark;
			&:hover,
			&:focus {
				color: @brand-primary;
				text-decoration: underline;
			}
		}
	}
	@media only screen and (min-width: 768px) {
		> a {
			> .post-title {
				font-size: 26px;
				line-height: 1.3;
				margin-bottom: 10px;
			}
			> .post-subtitle{
				font-size: 16px;
			}
		}
		.post-meta{
			font-size: 18px;
		}
	}
}

// Hux add home-page post-content-preview
.post-content-preview{
    font-size: 13px;
    font-style: italic;
    color:#a3a3a3;
    &:hover{
        color: @brand-primary;
    }
    @media only screen and (min-width: 768px) {
        font-size:14px;
    }
}

// Sections

.section-heading {
	font-size: 36px;
	margin-top: 60px;
	font-weight: 700;
}

.caption {
	text-align: center;
	font-size: 14px;
	padding: 10px;
	font-style: italic;
	margin: 0;
	display: block;
	border-bottom-right-radius: 5px;
	border-bottom-left-radius: 5px;
}

footer {
	font-size: 20px;
	padding: 50px 0 65px;
	.list-inline {
		margin: 0;
		padding: 0;
	}
	.copyright {
		font-size: 14px;
		text-align: center;
		margin-bottom: 0;
		a{
			color:#337ab7;
			// different to @brand-primary
			&:hover,
			&:focus {
				color: @brand-primary;
			}
		}
	}
}

// Contact Form Styles

.floating-label-form-group {
	font-size: 14px;
	position: relative;
	margin-bottom: 0;
	padding-bottom: 0.5em;
	border-bottom: 1px solid @gray-light;
	input,
	textarea {
		z-index: 1;
		position: relative;
		padding-right: 0;
		padding-left: 0;
		border: none;
		border-radius: 0;
		font-size: 1.5em;
		background: none;
		box-shadow: none !important;
		resize: none;
	}
	label {
		display: block;
		z-index: 0;
		position: relative;
		top: 2em;
		margin: 0;
		font-size: 0.85em;
		line-height: 1.764705882em;
		vertical-align: middle;
		vertical-align: baseline;
		opacity: 0;
		-webkit-transition: top 0.3s ease,opacity 0.3s ease;
		-moz-transition: top 0.3s ease,opacity 0.3s ease;
		-ms-transition: top 0.3s ease,opacity 0.3s ease;
		transition: top 0.3s ease,opacity 0.3s ease;
	}
	&::not(:first-child) {
		padding-left: 14px;
		border-left: 1px solid @gray-light;
	}
}

.floating-label-form-group-with-value {
	label {
		top: 0;
		opacity: 1;
	}
}

.floating-label-form-group-with-focus {
	label {
		color: @brand-primary;
	}
}

form .row:first-child .floating-label-form-group {
	border-top: 1px solid @gray-light;
}

// Button Styles

.btn {
	.sans-serif;
	text-transform: uppercase;
	font-size: 14px;
	font-weight: 800;
	letter-spacing: 1px;
	border-radius: 0;
	padding: 15px 25px;
}

.btn-lg {
	font-size: 16px;
	padding: 25px 35px;
}

.btn-default {
	&:hover,
	&:focus {
		background-color: @brand-primary;
		border: 1px solid @brand-primary;
		color: white;
	}
}

// Pager Styling

.pager {
	margin: 20px 0 0 !important;
	padding:0px !important;

	li {
		> a,
		> span {
			.sans-serif;
			text-transform: uppercase;
			font-size: 13px;
			font-weight: 800;
			letter-spacing: 1px;
			padding: 10px;
			background-color: white;
			border-radius: 0;
			@media only screen and (min-width: 768px) {
				font-size: 14px;
				padding: 15px 25px;
			}
		}

		> a {
			color: @gray-dark;
		}
		> a:hover,
		> a:focus {
			color: white;
			background-color: @brand-primary;
			border: 1px solid @brand-primary;

			// V1.6 display title
			> span{
				color: white;
			}
		}
	}

	.disabled {
		> a,
		> a:hover,
		> a:focus,
		> span {
			color: @gray;
			background-color: @gray-dark;
			cursor: not-allowed;
		}
	}
}

// -- Highlight Color Customization

::-moz-selection {
	color: white;
	text-shadow: none;
	background: @brand-primary;
}

::selection {
	color: white;
	text-shadow: none;
	background: @brand-primary;
}

img::selection {
	color: white;
	background: transparent;
}

img::-moz-selection {
	color: white;
	background: transparent;
}


/* Hux add tags support */
.tags{
	margin-bottom: -5px;
	a,.tag{
		display: inline-block;
		border: 1px solid rgba(255,255,255,0.8);
		border-radius: 999em;
		padding: 0 10px;
		color: rgba(255,255,255,1);
		line-height: 24px;
		font-size: 12px;
		text-decoration: none;
		margin: 0 1px;
		margin-bottom: 6px;
		&:hover, &:active{
			color:white;
			border-color: white;
			background-color: rgba(255,255,255,0.4);
			text-decoration: none;

		}
		@media only screen and (min-width: 768px) {
			margin-right: 5px;
		}
	}
}

#tag-heading{
	padding: 70px 0 60px;
	@media only screen and (min-width: 768px) {
		padding:55px 0;
	}

}
// tag_cloud
#tag_cloud{
	margin:20px 0 15px 0;
	a,.tag{
		font-size: 14px;
		border: none;
		line-height: 28px;
		margin: 0 2px;
		margin-bottom: 8px;
		background: #D6D6D6;
		&:hover, &:active{
			background-color: #0085a1 !important;
		}
	}
	@media only screen and (min-width: 768px) {
		margin-bottom: 25px;
	}
}

.tag-comments{
    font-size: 12px;
    	@media only screen and (min-width: 768px) {
		font-size:14px;
	}
}

.t{
	//margin-top: 50px;
	&:first-child{
		margin-top: 0px;
	}
	hr:last-of-type{
		//display: none;
	}
}
.listing-seperator{
	color:#0085a1;
	font-size: 21px !important;
	&::before{
		margin-right: 5px;
	}
	@media only screen and (min-width: 768px) {
		font-size: 20px !important;
		line-height: 2 !important;
	}
}

// Customize Style for Posts in One-Tag-List
.one-tag-list{
	.tag-text{
		font-weight: 200;
		.sans-serif;
	}
	.post-preview {
		position: relative;
		> a {
			.post-title{
				font-size: 16px;
				font-weight: 500;
				margin-top: 20px;
			}
			.post-subtitle{
				font-size: 12px;
			}
		}
		> .post-meta{
			position: absolute;
			right: 5px;
			bottom: 0px;
			margin: 0px;
			font-size: 12px;
			line-height: 12px;
		}
		@media only screen and (min-width: 768px) {
			margin-left: 20px;
			> a {
				> .post-title {
					font-size: 18px;
					line-height: 1.3
				}
				> .post-subtitle{
					font-size: 14px;
				}
			}
			.post-meta{
				font-size: 18px;
			}
		}
	}
}

/* Tags support End*/

/* Hux make all img responsible in post-container */
.post-container img{
	display: block;
	max-width: 100%;
	height: auto;
	margin: 1.5em auto 1.6em auto;
}


/* Hux Optimize UserExperience */
.navbar-default .navbar-toggle {
	&:focus, &:hover{
		background-color: inherit;
	}
  	&:active{
  		background-color: rgba(255, 255, 255, 0.25);
  	}
}

/* Hux customize Style for navBar button */

.navbar-default .navbar-toggle{
	border-color: transparent;
	padding:19px 16px;
	margin-top: 2px;
	margin-right: 2px;
	margin-bottom: 2px;
	border-radius: 50%;
	.icon-bar{
		width: 18px;
		border-radius: 0px;
		// materialize
		background-color: white;
	}
	.icon-bar+.icon-bar{
		margin-top: 3px;
	}
}




/* Giscus 评论系统样式 */
#giscus-container {
	margin-top: 30px;
	padding: 0;

	.giscus {
		max-width: 100%;
	}

	.giscus-frame {
		border-radius: 8px;
		border: 1px solid #e1e4e8;
		background-color: #fff;
	}
}

/* 响应式调整 */
@media (max-width: 768px) {
	#giscus-container {
		margin-top: 20px;
		padding: 0 10px;
	}
}


#ds-smilies-tooltip ul.ds-smilies-tabs li a {
	background: white !important;
}


// Hux fullscreen mode in 404.html

.page-fullscreen .intro-header{
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}
.page-fullscreen #tag-heading{
	position: fixed;
	left: 0;
	top: 0;
	padding-bottom: 150px;
	width: 100%;
	height: 100%;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-box-pack: center;
	-webkit-box-align: center;

	display: -webkit-flex;
	-webkit-align-items: center;
	-webkit-justify-content: center;
	-webkit-flex-direction: column;

	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}
.page-fullscreen footer{
	position: absolute;
	width: 100%;
	bottom: 0;
	padding-bottom: 20px;
	opacity: 0.6;
	color: #fff;
}
.page-fullscreen footer .copyright{
	color: #fff;
}
.page-fullscreen footer .copyright a{
	color: #fff;
}
.page-fullscreen footer .copyright a:hover{
	color: #ddd;
}



