---
layout: default
---

<!-- Image to hack wechat -->
<!-- <img src="/img/icon_wechat.png" width="0" height="0"> -->
<!-- <img src="{{ site.baseurl }}/{% if page.header-img %}{{ page.header-img }}{% else %}{{ site.header-img }}{% endif %}" width="0" height="0"> -->

<!-- Post Header -->
<style type="text/css">
    header.intro-header{
        position: relative;
        /* 在这里修改博客文章背景 */
        background: linear-gradient(135deg, #5bc2e7 0%, #8b5fbf 100%);
    }

    {% if page.header-mask %}
    header.intro-header .header-mask{
        width: 100%;
        height: 100%;
        position: absolute;
        background: rgba(0,0,0, {{ page.header-mask }});
    }
    {% endif %}

    /* 修复文章页面可能导致微小滚动条的元素 */
    .pager {
        margin: 0 !important;
    }

    hr[style*="visibility: hidden"] {
        margin: 0 !important;
        height: 0 !important;
        border: none !important;
    }

    #giscus-container {
        margin-bottom: 0 !important;
    }

    .sidebar-container:last-child {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }
</style>
<header class="intro-header" >
    <div class="header-mask"></div>
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1">
                <div class="post-heading">
                    <div class="tags">
                        {% for tag in page.tags %}
                        <a class="tag" href="{{ site.baseurl }}/tags/#{{ tag }}" title="{{ tag }}">{{ tag }}</a>
                        {% endfor %}
                    </div>
                    <h1>{{ page.title }}</h1>
                    {% comment %}
                        always create a h2 for keeping the margin , Hux
                    {% endcomment %}
                    {% comment %} if page.subtitle {% endcomment %}
                    <h2 class="subheading">{{ page.subtitle }}</h2>
                    {% comment %} endif {% endcomment %}
                    <span class="meta">Posted by {% if page.author %}{{ page.author }}{% else %}{{ site.title }}{% endif %} on {{ page.date | date: "%B %-d, %Y" }}</span>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Post Content -->
<article>
    <div class="container">
        <div class="row">

    <!-- Post Container -->
            <div class="
                col-lg-8 col-lg-offset-2
                col-md-10 col-md-offset-1
                post-container">

				{{ content }}

                <hr style="visibility: hidden;">

                <ul class="pager">
                    {% if page.previous.url %}
                    <li class="previous">
                        <a href="{{ page.previous.url | prepend: site.baseurl | replace: '//', '/' }}" data-toggle="tooltip" data-placement="top" title="{{page.previous.title}}">
                        Previous<br>
                        <span>{{page.previous.title}}</span>
                        </a>
                    </li>
                    {% endif %}
                    {% if page.next.url %}
                    <li class="next">
                        <a href="{{ page.next.url | prepend: site.baseurl | replace: '//', '/' }}" data-toggle="tooltip" data-placement="top" title="{{page.next.title}}">
                        Next<br>
                        <span>{{page.next.title}}</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Giscus 评论 start -->
                {% if site.giscus.enable %}
                <hr>
                <div id="giscus-container">
                    <script src="https://giscus.app/client.js"
                        data-repo="{{ site.giscus.repo }}"
                        data-repo-id="{{ site.giscus.repo-id }}"
                        data-category="{{ site.giscus.category }}"
                        data-category-id="{{ site.giscus.category-id }}"
                        data-mapping="{{ site.giscus.mapping }}"
                        data-strict="{{ site.giscus.strict }}"
                        data-reactions-enabled="{{ site.giscus.reactions-enabled }}"
                        data-emit-metadata="{{ site.giscus.emit-metadata }}"
                        data-input-position="{{ site.giscus.input-position }}"
                        data-theme="{{ site.giscus.theme }}"
                        data-lang="{{ site.giscus.lang }}"
                        data-loading="{{ site.giscus.loading }}"
                        crossorigin="anonymous"
                        async>
                    </script>
                </div>
                {% endif %}
                <!-- Giscus 评论 end -->

            </div>

    <!-- Side Catalog Container -->
        {% if page.catalog %}
            <div class="
                col-lg-2 col-lg-offset-0
                visible-lg-block
                sidebar-container
                catalog-container">
                <div class="side-catalog">
                    <hr class="hidden-sm hidden-xs">
                    <h5>
                        <a class="catalog-toggle" href="#">CATALOG</a>
                    </h5>
                    <ul class="catalog-body"></ul>
                </div>
            </div>
        {% endif %}

    <!-- Sidebar Container -->
            <div class="
                col-lg-8 col-lg-offset-2
                col-md-10 col-md-offset-1
                sidebar-container">

                <!-- Featured Tags -->
                {% if site.featured-tags %}
                <section>
                    <hr class="hidden-sm hidden-xs">
                    <h5><a href="/tags/">FEATURED TAGS</a></h5>
                    <div class="tags">
        				{% for tag in site.tags %}
                            {% if tag[1].size > {{site.featured-condition-size}} %}
                				<a href="/tags/#{{ tag[0] }}" title="{{ tag[0] }}" rel="{{ tag[1].size }}">
                                    {{ tag[0] }}
                                </a>
                            {% endif %}
        				{% endfor %}
        			</div>
                </section>
                {% endif %}


            </div>
        </div>
    </div>
</article>





{% if site.anchorjs %}
<!-- async load function -->
<script>
    function async(u, c) {
      var d = document, t = 'script',
          o = d.createElement(t),
          s = d.getElementsByTagName(t)[0];
      o.src = u;
      if (c) { o.addEventListener('load', function (e) { c(null, e); }, false); }
      s.parentNode.insertBefore(o, s);
    }
</script>
<!-- anchor-js, Doc:http://bryanbraun.github.io/anchorjs/ -->
<script>
    async("//cdnjs.cloudflare.com/ajax/libs/anchor-js/1.1.1/anchor.min.js",function(){
        anchors.options = {
          visible: 'always',
          placement: 'right',
          icon: '#'
        };
        anchors.add().remove('.intro-header h1').remove('.subheading').remove('.sidebar-container h5');
    })
</script>
<style>
    /* place left on bigger screen */
    @media all and (min-width: 800px) {
        .anchorjs-link{
            position: absolute;
            left: -0.75em;
            font-size: 1.1em;
            margin-top : -0.1em;
        }
    }
</style>
{% endif %}
