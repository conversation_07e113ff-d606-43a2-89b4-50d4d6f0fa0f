@media (min-width: 1200px) {
  .post-container,
  .sidebar-container {
    padding-right: 5%;
  }
}
@media (min-width: 768px) {
  .post-container {
    padding-right: 5%;
  }
}
.sidebar-container {
  color: #bfbfbf;
  font-size: 14px;
}
.sidebar-container h5 {
  color: #808080;
  padding-bottom: 1em;
}
.sidebar-container h5 a {
  color: #808080 !important;
  text-decoration: none;
}
.sidebar-container a {
  color: #bfbfbf !important;
}
.sidebar-container a:hover,
.sidebar-container a:active {
  color: #0085a1 !important;
}
.sidebar-container .tags a {
  border-color: #bfbfbf;
}
.sidebar-container .tags a:hover,
.sidebar-container .tags a:active {
  border-color: #0085a1;
}
.sidebar-container .short-about img {
  width: 80%;
  display: block;
  border-radius: 5px;
  margin-bottom: 20px;
}
.sidebar-container .short-about p {
  margin-top: 0px;
  margin-bottom: 20px;
}
.sidebar-container .short-about .list-inline > li {
  padding-left: 0px;
}
.catalog-container {
  padding: 0px;
}
.side-catalog {
  display: block;
  overflow: auto;
  height: 100%;
  padding-bottom: 40px;
  width: 195px;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.side-catalog::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.side-catalog.fixed {
  position: fixed;
  top: -21px;
}
.side-catalog.fold .catalog-toggle::before {
  content: "+";
}
.side-catalog.fold .catalog-body {
  display: none;
}
.side-catalog .catalog-toggle::before {
  content: "−";
  position: relative;
  margin-right: 5px;
  bottom: 1px;
}
.side-catalog .catalog-body {
  position: relative;
  list-style: none;
  height: auto;
  overflow: hidden;
  padding-left: 0px;
  padding-right: 5px;
  text-indent: 0;
}
.side-catalog .catalog-body li {
  position: relative;
  list-style: none;
}
.side-catalog .catalog-body li a {
  padding-left: 10px;
  max-width: 180px;
  display: inline-block;
  vertical-align: middle;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  text-decoration: none;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.side-catalog .catalog-body .h1_nav,
.side-catalog .catalog-body .h2_nav,
.side-catalog .catalog-body .h3_nav {
  margin-left: 0;
  font-size: 13px;
  font-weight: bold;
}
.side-catalog .catalog-body .h4_nav,
.side-catalog .catalog-body .h5_nav,
.side-catalog .catalog-body .h6_nav {
  margin-left: 10px;
  font-size: 12px;
}
.side-catalog .catalog-body .h4_nav a,
.side-catalog .catalog-body .h5_nav a,
.side-catalog .catalog-body .h6_nav a {
  max-width: 170px;
}
.side-catalog .catalog-body .active {
  border-radius: 4px;
  background-color: #F5F5F5;
}
.side-catalog .catalog-body .active a {
  color: #0085a1!important;
}
@media (max-width: 1200px) {
  .side-catalog {
    display: none;
  }
}
body {
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
  font-size: 16px;
  color: #404040;
  overflow-x: hidden;
  /* 隐藏页面滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
body::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
html {
  /* 隐藏html滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
html::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
/* 全局隐藏所有滚动条 */
*::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
p {
  margin: 30px 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
  line-height: 1.1;
  font-weight: bold;
}
h4 {
  font-size: 21px;
}
a {
  color: #404040;
}
a:hover,
a:focus {
  color: #0085a1;
}
a img:hover,
a img:focus {
  cursor: zoom-in;
}
article {
  overflow-x: hidden;
}
blockquote {
  color: #808080;
  font-style: italic;
  font-size: 0.95em;
  margin: 20px 0 20px;
}
blockquote p {
  margin: 0;
}
small.img-hint {
  display: block;
  margin-top: -20px;
  text-align: center;
}
br + small.img-hint {
  margin-top: -40px;
}
img.shadow {
  box-shadow: rgba(0, 0, 0, 0.258824) 0px 2px 5px 0px;
}
select {
  -webkit-appearance: none;
  margin-top: 15px;
  color: #337ab7;
  border-color: #337ab7;
  padding: 0em 0.4em;
  background: white;
}
select.sel-lang {
  min-height: 28px;
  font-size: 14px;
}
.table th,
.table td {
  border: 1px solid #eee !important;
}
hr.small {
  max-width: 100px;
  margin: 15px auto;
  border-width: 4px;
  border-color: white;
}
pre,
.table-responsive {
  -webkit-overflow-scrolling: touch;
}
pre code {
  display: block;
  width: auto;
  white-space: pre;
  word-wrap: normal;
}
.postlist-container {
  margin-bottom: 15px;
}
.post-container a {
  color: #337ab7;
}
.post-container a:hover,
.post-container a:focus {
  color: #0085a1;
}
.post-container h1,
.post-container h2,
.post-container h3,
.post-container h4,
.post-container h5,
.post-container h6 {
  margin: 30px 0 10px;
}
.post-container h5 {
  font-size: 19px;
  font-weight: 600;
  color: gray;
}
.post-container h5 + p {
  margin-top: 5px;
}
.post-container h6 {
  font-size: 16px;
  font-weight: 600;
  color: gray;
}
.post-container h6 + p {
  margin-top: 5px;
}
.post-container ul,
.post-container ol {
  margin-bottom: 40px;
}
@media screen and (max-width: 768px) {
  .post-container ul,
  .post-container ol {
    padding-left: 30px;
  }
}
@media screen and (max-width: 500px) {
  .post-container ul,
  .post-container ol {
    padding-left: 20px;
  }
}
.post-container ol ol,
.post-container ol ul,
.post-container ul ol,
.post-container ul ul {
  margin-bottom: 5px;
}
.post-container li p {
  margin: 0;
  margin-bottom: 5px;
}
.post-container li h1,
.post-container li h2,
.post-container li h3,
.post-container li h4,
.post-container li h5,
.post-container li h6 {
  line-height: 2;
  margin-top: 20px;
}
.post-container .pager li {
  width: 48%;
}
.post-container .pager li.next {
  float: right;
}
.post-container .pager li.previous {
  float: left;
}
.post-container .pager li > a {
  width: 100%;
}
.post-container .pager li > a > span {
  color: #808080;
  font-weight: normal;
  letter-spacing: 0.5px;
}
@media only screen and (max-width: 767px) {
  /**
	 * Layout
	 * Since V1.6 we use absolute positioning to prevent to expand container-fluid
	 * which would cover tags. A absolute positioning make a new layer.
	 */
  .navbar-default .navbar-collapse {
    position: absolute;
    right: 0;
    border: none;
    background: white;
    box-shadow: 0px 5px 10px 2px rgba(0, 0, 0, 0.2);
    box-shadow: rgba(0, 0, 0, 0.117647) 0px 1px 6px, rgba(0, 0, 0, 0.239216) 0px 1px 4px;
    border-radius: 2px;
    width: 170px;
  }
  /**
	 * Animation
	 * HuxBlog-Navbar using genuine Material Design Animation
	 */
  #huxblog_navbar {
    /**
		 * Sharable code and 'out' function
		 */
    opacity: 0;
    transform: scaleX(0);
    transform-origin: top right;
    transition: all 200ms cubic-bezier(0.47, 0, 0.4, 0.99) 0ms;
    -webkit-transform: scaleX(0);
    -webkit-transform-origin: top right;
    -webkit-transition: all 200ms cubic-bezier(0.47, 0, 0.4, 0.99) 0ms;
    /**
		 *'In' Animation
		 */
  }
  #huxblog_navbar a {
    font-size: 13px;
    line-height: 28px;
  }
  #huxblog_navbar .navbar-collapse {
    height: 0px;
    transform: scaleY(0);
    transform-origin: top right;
    transition: transform 400ms cubic-bezier(0.32, 1, 0.23, 1) 0ms;
    -webkit-transform: scaleY(0);
    -webkit-transform-origin: top right;
    -webkit-transition: -webkit-transform 400ms cubic-bezier(0.32, 1, 0.23, 1) 0ms;
  }
  #huxblog_navbar li {
    opacity: 0;
    transition: opacity 100ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
    -webkit-transition: opacity 100ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
  }
  #huxblog_navbar.in {
    transform: scaleX(1);
    -webkit-transform: scaleX(1);
    opacity: 1;
    transition: all 250ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
    -webkit-transition: all 250ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
  }
  #huxblog_navbar.in .navbar-collapse {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
    transition: transform 500ms cubic-bezier(0.23, 1, 0.32, 1);
    -webkit-transition: -webkit-transform 500ms cubic-bezier(0.23, 1, 0.32, 1);
  }
  #huxblog_navbar.in li {
    opacity: 1;
    transition: opacity 450ms cubic-bezier(0.23, 1, 0.32, 1) 205ms;
    -webkit-transition: opacity 450ms cubic-bezier(0.23, 1, 0.32, 1) 205ms;
  }
}
.navbar-custom {
  background: none;
  border: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3;
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
}
.navbar-custom .navbar-brand {
  font-weight: 800;
  color: white;
  height: 56px;
  line-height: 25px;
}
.navbar-custom .navbar-brand:hover {
  color: rgba(255, 255, 255, 0.8);
}
.navbar-custom .nav li a {
  text-transform: uppercase;
  font-size: 12px;
  line-height: 20px;
  font-weight: 800;
  letter-spacing: 1px;
}
.navbar-custom .nav li a:active {
  background: rgba(0, 0, 0, 0.12);
}
@media only screen and (min-width: 768px) {
  .navbar-custom {
    background: transparent;
    border-bottom: 1px solid transparent;
  }
  .navbar-custom body {
    font-size: 20px;
  }
  .navbar-custom .navbar-brand {
    color: white;
    padding: 20px;
    line-height: 20px;
  }
  .navbar-custom .navbar-brand:hover,
  .navbar-custom .navbar-brand:focus {
    color: rgba(255, 255, 255, 0.8);
  }
  .navbar-custom .nav li a {
    color: white;
    padding: 20px;
  }
  .navbar-custom .nav li a:hover,
  .navbar-custom .nav li a:focus {
    color: rgba(255, 255, 255, 0.8);
  }
  .navbar-custom .nav li a:active {
    background: none;
  }
}
@media only screen and (min-width: 1170px) {
  .navbar-custom {
    -webkit-transition: background-color 0.3s;
    -moz-transition: background-color 0.3s;
    transition: background-color 0.3s;
    /* Force Hardware Acceleration in WebKit */
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  .navbar-custom.is-fixed {
    /* when the user scrolls down, we hide the header right above the viewport */
    position: fixed;
    top: -61px;
    background-color: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid #f2f2f2;
    -webkit-transition: -webkit-transform 0.3s;
    -moz-transition: -moz-transform 0.3s;
    transition: transform 0.3s;
  }
  .navbar-custom.is-fixed .navbar-brand {
    color: #404040;
  }
  .navbar-custom.is-fixed .navbar-brand:hover,
  .navbar-custom.is-fixed .navbar-brand:focus {
    color: #0085a1;
  }
  .navbar-custom.is-fixed .nav li a {
    color: #404040;
  }
  .navbar-custom.is-fixed .nav li a:hover,
  .navbar-custom.is-fixed .nav li a:focus {
    color: #0085a1;
  }
  .navbar-custom.is-visible {
    /* if the user changes the scrolling direction, we show the header */
    -webkit-transform: translate3d(0, 100%, 0);
    -moz-transform: translate3d(0, 100%, 0);
    -ms-transform: translate3d(0, 100%, 0);
    -o-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
.intro-header {
  background: no-repeat center center;
  background-color: #808080;
  background-attachment: scroll;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  background-size: cover;
  -o-background-size: cover;
  margin-bottom: 0px;
  /* 0 on mobile, modify by Hux */
}
@media only screen and (min-width: 768px) {
  .intro-header {
    margin-bottom: 20px;
    /* response on desktop */
  }
}
.intro-header .site-heading,
.intro-header .post-heading,
.intro-header .page-heading {
  padding: 85px 0 55px;
  color: white;
}
@media only screen and (min-width: 768px) {
  .intro-header .site-heading,
  .intro-header .post-heading,
  .intro-header .page-heading {
    padding: 150px 0;
  }
}
.intro-header .site-heading {
  padding: 95px 0 70px;
}
@media only screen and (min-width: 768px) {
  .intro-header .site-heading {
    padding: 150px 0;
  }
}
.intro-header .site-heading,
.intro-header .page-heading {
  text-align: center;
}
.intro-header .site-heading h1,
.intro-header .page-heading h1 {
  margin-top: 0;
  font-size: 50px;
}
.intro-header .site-heading .subheading,
.intro-header .page-heading .subheading {
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
  font-size: 18px;
  line-height: 1.1;
  display: block;
  font-weight: 300;
  margin: 10px 0 0;
}
@media only screen and (min-width: 768px) {
  .intro-header .site-heading h1,
  .intro-header .page-heading h1 {
    font-size: 80px;
  }
}
.intro-header .post-heading h1 {
  font-size: 30px;
  margin-bottom: 24px;
}
.intro-header .post-heading .subheading,
.intro-header .post-heading .meta {
  line-height: 1.1;
  display: block;
}
.intro-header .post-heading .subheading {
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
  font-size: 17px;
  line-height: 1.4;
  font-weight: normal;
  margin: 10px 0 30px;
  margin-top: -5px;
}
.intro-header .post-heading .meta {
  font-family: 'Lora', 'Times New Roman', serif;
  font-style: italic;
  font-weight: 300;
  font-size: 18px;
}
.intro-header .post-heading .meta a {
  color: white;
}
@media only screen and (min-width: 768px) {
  .intro-header .post-heading h1 {
    font-size: 55px;
  }
  .intro-header .post-heading .subheading {
    font-size: 30px;
  }
  .intro-header .post-heading .meta {
    font-size: 20px;
  }
}
.post-preview > a {
  color: #404040;
}
.post-preview > a:hover,
.post-preview > a:focus {
  text-decoration: none;
  color: #0085a1;
}
.post-preview > a > .post-title {
  font-size: 21px;
  line-height: 1.3;
  margin-top: 30px;
  margin-bottom: 8px;
}
.post-preview > a > .post-subtitle {
  font-size: 15px;
  line-height: 1.3;
  margin: 0;
  font-weight: 300;
  margin-bottom: 10px;
}
.post-preview > .post-meta {
  font-family: 'Lora', 'Times New Roman', serif;
  color: #808080;
  font-size: 16px;
  font-style: italic;
  margin-top: 0;
}
.post-preview > .post-meta > a {
  text-decoration: none;
  color: #404040;
}
.post-preview > .post-meta > a:hover,
.post-preview > .post-meta > a:focus {
  color: #0085a1;
  text-decoration: underline;
}
@media only screen and (min-width: 768px) {
  .post-preview > a > .post-title {
    font-size: 26px;
    line-height: 1.3;
    margin-bottom: 10px;
  }
  .post-preview > a > .post-subtitle {
    font-size: 16px;
  }
  .post-preview .post-meta {
    font-size: 18px;
  }
}
.post-content-preview {
  font-size: 13px;
  font-style: italic;
  color: #a3a3a3;
}
.post-content-preview:hover {
  color: #0085a1;
}
@media only screen and (min-width: 768px) {
  .post-content-preview {
    font-size: 14px;
  }
}
.section-heading {
  font-size: 36px;
  margin-top: 60px;
  font-weight: 700;
}
.caption {
  text-align: center;
  font-size: 14px;
  padding: 10px;
  font-style: italic;
  margin: 0;
  display: block;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}
footer {
  font-size: 20px;
  padding: 50px 0 65px;
}
footer .list-inline {
  margin: 0;
  padding: 0;
}
footer .copyright {
  font-size: 14px;
  text-align: center;
  margin-bottom: 0;
}
footer .copyright a {
  color: #337ab7;
}
footer .copyright a:hover,
footer .copyright a:focus {
  color: #0085a1;
}
.floating-label-form-group {
  font-size: 14px;
  position: relative;
  margin-bottom: 0;
  padding-bottom: 0.5em;
  border-bottom: 1px solid #eeeeee;
}
.floating-label-form-group input,
.floating-label-form-group textarea {
  z-index: 1;
  position: relative;
  padding-right: 0;
  padding-left: 0;
  border: none;
  border-radius: 0;
  font-size: 1.5em;
  background: none;
  box-shadow: none !important;
  resize: none;
}
.floating-label-form-group label {
  display: block;
  z-index: 0;
  position: relative;
  top: 2em;
  margin: 0;
  font-size: 0.85em;
  line-height: 1.764705882em;
  vertical-align: middle;
  vertical-align: baseline;
  opacity: 0;
  -webkit-transition: top 0.3s ease,opacity 0.3s ease;
  -moz-transition: top 0.3s ease,opacity 0.3s ease;
  -ms-transition: top 0.3s ease,opacity 0.3s ease;
  transition: top 0.3s ease,opacity 0.3s ease;
}
.floating-label-form-group::not(:first-child) {
  padding-left: 14px;
  border-left: 1px solid #eeeeee;
}
.floating-label-form-group-with-value label {
  top: 0;
  opacity: 1;
}
.floating-label-form-group-with-focus label {
  color: #0085a1;
}
form .row:first-child .floating-label-form-group {
  border-top: 1px solid #eeeeee;
}
.btn {
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 800;
  letter-spacing: 1px;
  border-radius: 0;
  padding: 15px 25px;
}
.btn-lg {
  font-size: 16px;
  padding: 25px 35px;
}
.btn-default:hover,
.btn-default:focus {
  background-color: #0085a1;
  border: 1px solid #0085a1;
  color: white;
}
.pager {
  margin: 20px 0 0 !important;
  padding: 0px !important;
}
.pager li > a,
.pager li > span {
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 800;
  letter-spacing: 1px;
  padding: 10px;
  background-color: white;
  border-radius: 0;
}
@media only screen and (min-width: 768px) {
  .pager li > a,
  .pager li > span {
    font-size: 14px;
    padding: 15px 25px;
  }
}
.pager li > a {
  color: #404040;
}
.pager li > a:hover,
.pager li > a:focus {
  color: white;
  background-color: #0085a1;
  border: 1px solid #0085a1;
}
.pager li > a:hover > span,
.pager li > a:focus > span {
  color: white;
}
.pager .disabled > a,
.pager .disabled > a:hover,
.pager .disabled > a:focus,
.pager .disabled > span {
  color: #808080;
  background-color: #404040;
  cursor: not-allowed;
}
::-moz-selection {
  color: white;
  text-shadow: none;
  background: #0085a1;
}
::selection {
  color: white;
  text-shadow: none;
  background: #0085a1;
}
img::selection {
  color: white;
  background: transparent;
}
img::-moz-selection {
  color: white;
  background: transparent;
}
/* Hux add tags support */
.tags {
  margin-bottom: -5px;
}
.tags a,
.tags .tag {
  display: inline-block;
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 999em;
  padding: 0 10px;
  color: #ffffff;
  line-height: 24px;
  font-size: 12px;
  text-decoration: none;
  margin: 0 1px;
  margin-bottom: 6px;
}
.tags a:hover,
.tags .tag:hover,
.tags a:active,
.tags .tag:active {
  color: white;
  border-color: white;
  background-color: rgba(255, 255, 255, 0.4);
  text-decoration: none;
}
@media only screen and (min-width: 768px) {
  .tags a,
  .tags .tag {
    margin-right: 5px;
  }
}
#tag-heading {
  padding: 70px 0 60px;
}
@media only screen and (min-width: 768px) {
  #tag-heading {
    padding: 55px 0;
  }
}
#tag_cloud {
  margin: 20px 0 15px 0;
}
#tag_cloud a,
#tag_cloud .tag {
  font-size: 14px;
  border: none;
  line-height: 28px;
  margin: 0 2px;
  margin-bottom: 8px;
  background: #D6D6D6;
}
#tag_cloud a:hover,
#tag_cloud .tag:hover,
#tag_cloud a:active,
#tag_cloud .tag:active {
  background-color: #0085a1 !important;
}
@media only screen and (min-width: 768px) {
  #tag_cloud {
    margin-bottom: 25px;
  }
}
.tag-comments {
  font-size: 12px;
}
@media only screen and (min-width: 768px) {
  .tag-comments {
    font-size: 14px;
  }
}
.t:first-child {
  margin-top: 0px;
}
.listing-seperator {
  color: #0085a1;
  font-size: 21px !important;
}
.listing-seperator::before {
  margin-right: 5px;
}
@media only screen and (min-width: 768px) {
  .listing-seperator {
    font-size: 20px !important;
    line-height: 2 !important;
  }
}
.one-tag-list .tag-text {
  font-weight: 200;
  /* Hux learn from
     *     TypeIsBeautiful,
     *     [This Post](http://zhuanlan.zhihu.com/ibuick/20186806) etc.
     */
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Arial", "PingFang SC", "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", "Source Han Sans SC", "Noto Sans CJK SC", "Source Han Sans CN", "Noto Sans SC", "Source Han Sans TC", "Noto Sans CJK TC", "WenQuanYi Micro Hei", SimSun, sans-serif;
  line-height: 1.7;
}
.one-tag-list .post-preview {
  position: relative;
}
.one-tag-list .post-preview > a .post-title {
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
}
.one-tag-list .post-preview > a .post-subtitle {
  font-size: 12px;
}
.one-tag-list .post-preview > .post-meta {
  position: absolute;
  right: 5px;
  bottom: 0px;
  margin: 0px;
  font-size: 12px;
  line-height: 12px;
}
@media only screen and (min-width: 768px) {
  .one-tag-list .post-preview {
    margin-left: 20px;
  }
  .one-tag-list .post-preview > a > .post-title {
    font-size: 18px;
    line-height: 1.3;
  }
  .one-tag-list .post-preview > a > .post-subtitle {
    font-size: 14px;
  }
  .one-tag-list .post-preview .post-meta {
    font-size: 18px;
  }
}
/* Tags support End*/
/* Hux make all img responsible in post-container */
.post-container img {
  display: block;
  max-width: 100%;
  height: auto;
  margin: 1.5em auto 1.6em auto;
}
/* Hux Optimize UserExperience */
.navbar-default .navbar-toggle:focus,
.navbar-default .navbar-toggle:hover {
  background-color: inherit;
}
.navbar-default .navbar-toggle:active {
  background-color: rgba(255, 255, 255, 0.25);
}
/* Hux customize Style for navBar button */
.navbar-default .navbar-toggle {
  border-color: transparent;
  padding: 19px 16px;
  margin-top: 2px;
  margin-right: 2px;
  margin-bottom: 2px;
  border-radius: 50%;
}
.navbar-default .navbar-toggle .icon-bar {
  width: 18px;
  border-radius: 0px;
  background-color: white;
}
.navbar-default .navbar-toggle .icon-bar + .icon-bar {
  margin-top: 3px;
}
/* Hux customize Style for Duoshuo */
.comment {
  margin-top: 20px;
}
.comment #ds-thread #ds-reset a.ds-like-thread-button {
  border: 1px solid #ddd;
  border-radius: 0px;
  background: white;
  box-shadow: none;
  text-shadow: none;
}
.comment #ds-thread #ds-reset li.ds-tab a.ds-current {
  border: 1px solid #ddd;
  border-radius: 0px;
  background: white;
  box-shadow: none;
  text-shadow: none;
}
.comment #ds-thread #ds-reset .ds-textarea-wrapper {
  background: none;
}
.comment #ds-thread #ds-reset .ds-gradient-bg {
  background: none;
}
.comment #ds-thread #ds-reset .ds-post-options {
  border-bottom: 1px solid #ccc;
}
.comment #ds-thread #ds-reset .ds-post-button {
  border-bottom: 1px solid #ccc;
}
.comment #ds-thread #ds-reset .ds-post-button {
  background: white;
  box-shadow: none;
}
.comment #ds-thread #ds-reset .ds-post-button:hover {
  background: #eeeeee;
}
#ds-smilies-tooltip ul.ds-smilies-tabs li a {
  background: white !important;
}


.page-fullscreen .intro-header {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.page-fullscreen #tag-heading {
  position: fixed;
  left: 0;
  top: 0;
  padding-bottom: 150px;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  display: -webkit-flex;
  -webkit-align-items: center;
  -webkit-justify-content: center;
  -webkit-flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.page-fullscreen footer {
  position: absolute;
  width: 100%;
  bottom: 0;
  padding-bottom: 20px;
  opacity: 0.6;
  color: #fff;
}
.page-fullscreen footer .copyright {
  color: #fff;
}
.page-fullscreen footer .copyright a {
  color: #fff;
}
.page-fullscreen footer .copyright a:hover {
  color: #ddd;
}
