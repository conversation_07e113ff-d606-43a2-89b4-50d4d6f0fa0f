---
layout:     post                    # 使用的布局（不需要改）
title:      从GPT-4 Turbo的发布，漫谈AI的发展趋势              # 标题 
subtitle:   AI Agent展望 #副标题
date:       2024-01-09              # 时间
author:     Keyon                      # 作者
catalog: true                       # 是否归档
tags:
  科技
---

## GPT都更新了什么，干掉了哪些创业公司

被誉为科技春晚的苹果发布会是越来越难看了，说实话还是OpenAI发布会更有看头。科技春晚的“名誉”被OpenAI替代应该是早晚的事，从这里可以看出产业变迁趋势轰轰烈烈。

短短45分钟，Altman介绍了目前最强的大模型，也讲解了GPT的迭代更新。在我看来，GPT最重要的升级是以下几点：

第一，更长的上下文长度，容量相当于一本300页的书（128K tokens）。

这个看似常规的升级实则影响面很大，首当其冲的应该是向量数据库。曾经的GPT-3.5/4是有天然缺陷的，例如GPT-3.5 Turbo的上下文限制大约是3000个字（4K tokens），并且GPT API按照tokens使用量收取费用，使用tokens越多收费越贵。开发者通过引入向量数据库，可以降低tokens使用成本，以及提高响应时间，最重要的是可以绕开tokens的限制问题。

然而这次升级解决了上述所有问题，直接消灭了向量数据库引入大模型生态的理由。更长的上下文长度使得绕开tokens的限制问题成了伪需求，GPT-4 Turbo的价格降低到GPT-4的1/3，并且推理速度达到了每分钟两倍tokens。

不出意外，向量数据库在大模型领域的火爆应该由此终止了。不过它作为广泛应用于推荐系统的技术，仍然还是会发展的。

第二，定制化和多模态。

GPT4微调等定制模型服务统统可以在OpenAI的平台中完成，融合Dall-E 3、TTS等模型构建GPT的多模态能力，用户可以通过调用API直接获取这些能力。

被这项升级直接冲击到的应该是LangChain这类曾经估值很高的中间件公司，这类公司不训练大模型，它们通过开发工具链解决上述GPT的原生痛点，以及帮助其他开发者打通不同技术之间的链条，例如通过LangChain使大模型调用向量数据库。

然而定制化服务和多模态能力极大的丰富了GPT的创造能力，无疑会挤压大模型中间件公司的生存空间。

第三，打造AI Agent商城，与开发者分享收益。

在我看来这个才是最劲爆的，这条消息意味着OpenAI全面向Apple学习，要把GPT Store打造成AI时代的Apple Store。

从这里看，OpenAI对自身定位非常清晰，做生态的意图非常明显。虽然这次发布会的使很多AI创业公司价值一夜清零，但OpenAI的战略方向并非垄断。或者说，他垄断的是大模型基座，在他的基座的基础上，欢迎大家一起共建生态。

任正非说过一句话：合纵连横的目标，不是为了称霸，而是为了合理、均衡。不给他人留活路的公司是活不了太久的，打造生态、开放共赢才是更长久的战略，想必这个顶级的AI团队也明白这个道理。

## AI的真正的机会是什么

> 以史为鉴，可以知兴替。

OpenAI发布会后，初创AI公司一片哀嚎，媒体也跟风大肆炒作渲染悲观气氛。但，创业者的机会真的被GPT降维打击了吗，我看未必。

或者我说一个暴论：**能被OpenAI干掉的机会，都不是真机会。**

Tensorflow、PyTorch等深度学习平台使得算法研发转变为搭积木的范式，使得深度学习算法工程师可以专注于业务问题和模型设计。

而现在，不仅深度学习的开发框架成为了平台，深度学习模型本身也正在成为新的基础设施，如果对标Apple，目前的GPT更像是iOS。

GPT等大模型的作用也是提供一个类似的框架平台，在这个平台上的开发者无需再开发工具类的应用，只需要用平台提供的All Tools和Agent就能快速搭建应用，使生态参与者仅需解决商业问题。

各种Agent类似于App Store里的应用，顺便一提，这次发布会的GPT Store完全没有给第三方开发应用（Agent）商城的机会，OpenAI牢牢握紧了应用的流量分发入口。

在移动互联网时代，工具类的应用会很自然的被操作系统厂商替代的，例如计算器、手机相册，现在绝大多数人会直接使用系统自带的工具类应用。相应的，ChatPDF、LangChain之类的纯工具应用会被GPT替代，或者直接被现有工具集成（类似OpenAI与Microsoft的合作）。

所以在OpenAI的生态里，创新的机会不在于工具类应用，而在于面向用户需求的开发。这和张小龙开发微信并没有什么不同，需要的是对大模型基础设施能力的了然于胸，更重要的还是对人性的深刻洞察。

在这次AI浪潮中一定会涌现出来很多解决用户需求的应用（Agent），但其中有些应用注定是昙花一现的假机会，例如风靡一时的妙鸭相机。由于其功能过于单一，且没有实物资产作为护城河，该类应用没有持久的商业模式作为支撑，所以我称之为假机会。

移动互联网时代的真机会，是微信、淘宝、京东、美团、抖音，从这个角度看，AI时代的真机会还远未到来。

## AI相关的从业人员可以做什么

以下内容，仅从AI技术相关从业者的角度出发，来聊聊现在的AI发展趋势。

AI这一波浪潮十分迅猛，相比移动互联网时代发展的初期，节奏也更快一些，我预计明年GPT-5发布的时候，会有更有意思的东西涌现出来。

目前从算法层面来看，线性模型、卷积神经网络等算法架构都已经触达到了智能顶点，而对Transformer架构的探索也还处于中期阶段。现在入行的技术人员可以少关注传统机器学习模型，把注意力更多放到Transformer相关的模型架构上。

在工业界，技术的升级也不再局限于依赖单个算法层面的突破，已经逐步演变为系统层面的整体技术体系的演进。大模型需要与分布式系统、大数据平台深度整合才能得到最好的能力发挥，数据、算力、模型等任何一方出现短板，都会对整个系统的AI能力形成制约。

其次，除了少数训练大模型（做操作系统）的公司，绝大多数公司会是以开发Agent（做应用）为主。这就要求相关从业者更多的从务实的角度出发，思考通过Agent解决业务问题。从战略路径上看，即科技赋能业务，业务反哺科技。

所以，技术团队应该有技术自信，不过分追求技术前沿，不拿着锤子在业务场景四处找钉子。而是作为行业专家，根据问题找相应的技术工具。
