---
layout: default
---

<!-- Image to hack wechat -->
<!-- <img src="/img/icon_wechat.png" width="0" height="0"> -->
<!-- <img src="{{ site.baseurl }}/{% if page.header-img %}{{ page.header-img }}{% else %}{{ site.header-img }}{% endif %}" width="0" height="0"> -->

<!-- Post Header -->
<style type="text/css">
    header.intro-header{
        /*background-image: url('{{ site.baseurl }}/{% if page.header-img %}{{ page.header-img }}{% else %}{{ site.header-img }}{% endif %}')*/
        height: 500px;
        overflow: hidden;
    }
    header iframe{
        width: 100%;
        height: 100%;
        border: 0;
    }
    /* Override Nav Style */
    {% if page.navcolor == "invert" %}
        .navbar-custom .nav li a,
        .navbar-custom .nav li a:hover,
        .navbar-custom .navbar-brand,
        .navbar-custom .navbar-brand:hover {color:#777;}
        .navbar-default .navbar-toggle .icon-bar {background-color:#777;}

    {% endif %}
</style>
<header class="intro-header" >
    <iframe src="{{page.iframe}}"/>
        <div class="container">
            <div class="row">
                <div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1">
                    <div class="post-heading">
                        <div class="tags">
                            {% for tag in page.tags %}
                            <a class="tag" href="{{ site.baseurl }}/tags/#{{ tag }}" title="{{ tag }}">{{ tag }}</a>
                            {% endfor %}
                        </div>
                        <h1>{{ page.title }}</h1>
                        {% comment %}
                            always create a h2 for keeping the margin , Hux
                        {% endcomment %}
                        {% comment %} if page.subtitle {% endcomment %}
                        <h2 class="subheading">{{ page.subtitle }}</h2>
                        {% comment %} endif {% endcomment %}
                        <span class="meta">Posted by {% if page.author %}{{ page.author }}{% else %}{{ site.title }}{% endif %} on {{ page.date | date: "%B %-d, %Y" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </iframe>
</header>

<!-- Post Content -->
<article>
    <div class="container">
        <div class="row">

            <!-- Post Container -->
            <div class="post-container
                col-lg-8 col-lg-offset-2
                col-md-10 col-md-offset-1 ">

				{{ content }}

                <hr style="visibility: hidden;">


                <ul class="pager">
                    {% if page.previous.url %}
                    <li class="previous">
                        <a href="{{ page.previous.url | prepend: site.baseurl | replace: '//', '/' }}" data-toggle="tooltip" data-placement="top" title="{{page.previous.title}}">
                        Previous<br>
                        <span>{{page.previous.title}}</span>
                        </a>
                    </li>
                    {% endif %}
                    {% if page.next.url %}
                    <li class="next">
                        <a href="{{ page.next.url | prepend: site.baseurl | replace: '//', '/' }}" data-toggle="tooltip" data-placement="top" title="{{page.next.title}}">
                        Next<br>
                        <span>{{page.next.title}}</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Giscus 评论 start -->
                {% if site.giscus.enable %}
                <hr>
                <div id="giscus-container">
                    <script src="https://giscus.app/client.js"
                        data-repo="{{ site.giscus.repo }}"
                        data-repo-id="{{ site.giscus.repo-id }}"
                        data-category="{{ site.giscus.category }}"
                        data-category-id="{{ site.giscus.category-id }}"
                        data-mapping="{{ site.giscus.mapping }}"
                        data-strict="{{ site.giscus.strict }}"
                        data-reactions-enabled="{{ site.giscus.reactions-enabled }}"
                        data-emit-metadata="{{ site.giscus.emit-metadata }}"
                        data-input-position="{{ site.giscus.input-position }}"
                        data-theme="{{ site.giscus.theme }}"
                        data-lang="{{ site.giscus.lang }}"
                        data-loading="{{ site.giscus.loading }}"
                        crossorigin="anonymous"
                        async>
                    </script>
                </div>
                {% endif %}
                <!-- Giscus 评论 end -->



            </div>

            <!-- Sidebar Container -->
            <div class="sidebar-container
                col-lg-8 col-lg-offset-2
                col-md-10 col-md-offset-1 ">

                <!-- Featured Tags -->
                {% if site.featured-tags %}
                <section>
                    <hr class="hidden-sm hidden-xs">
                    <h5><a href="/tags/">FEATURED TAGS</a></h5>
                    <div class="tags">
                        {% for tag in site.tags %}
                            {% if tag[1].size > {{site.featured-condition-size}} %}
                                <a href="/tags/#{{ tag[0] }}" title="{{ tag[0] }}" rel="{{ tag[1].size }}">
                                    {{ tag[0] }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </div>
                </section>
                {% endif %}


            </div>
        </div>
    </div>
</article>

<!-- resize header to fullscreen keynotes -->
<script>
    var $header = document.getElementsByTagName("header")[0];
    function resize(){
        /*
         * leave 85px to both
         * - told/imply users that there has more content below
         * - let user can scroll in mobile device, seeing the keynote-view is unscrollable
         */
        $header.style.height = (window.innerHeight-85) + 'px';
    }
    document.addEventListener('DOMContentLoaded', function(){
        resize();
    })
    window.addEventListener('load', function(){
        resize();
    })
    window.addEventListener('resize', function(){
        resize();
    })
    resize();
</script>






{% if site.anchorjs %}
<!-- async load function -->
<script>
    function async(u, c) {
      var d = document, t = 'script',
          o = d.createElement(t),
          s = d.getElementsByTagName(t)[0];
      o.src = u;
      if (c) { o.addEventListener('load', function (e) { c(null, e); }, false); }
      s.parentNode.insertBefore(o, s);
    }
</script>
<!-- anchor-js, Doc:http://bryanbraun.github.io/anchorjs/ -->
<script>
    async("//cdnjs.cloudflare.com/ajax/libs/anchor-js/1.1.1/anchor.min.js",function(){
        anchors.options = {
          visible: 'always',
          placement: 'right',
          icon: '#'
        };
        anchors.add().remove('.intro-header h1').remove('.subheading').remove('.sidebar-container h5');
    })
</script>
<style>
    /* place left on bigger screen */
    @media all and (min-width: 800px) {
        .anchorjs-link{
            position: absolute;
            left: -0.75em;
            font-size: 1.1em;
            margin-top : -0.1em;
        }
    }
</style>
{% endif %}
