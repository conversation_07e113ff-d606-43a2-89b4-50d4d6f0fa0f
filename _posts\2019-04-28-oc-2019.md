---
layout:     post                    # 使用的布局（不需要改）
title:      总结一些的好课              # 标题 
subtitle:   为学过的优质课程作标注 #副标题
date:       2019-04-28              # 时间
author:     Keyon                      # 作者
catalog: true                       # 是否归档
tags:
 公开课
---

## 公开课

| 课程 | 教师 | 大学 | 是否学完 |
| :----: | :----: | :----: | :----: |
| [CS50](https://certificates.cs50.io/0f999b74-664c-4f1f-92e4-4204692d7289.png?size=letter) | <PERSON> | Harvard University | ✅ |
| Justice: What's the Right Thing to Do? | <PERSON> | Harvard University | ✅ |
| Positive Psychology | Tal <PERSON> | Harvard University | ✅ |
| Statistics 110: Probability | <PERSON> | Harvard University |  |
| Advanced Algorithms (COMPSCI 224) | Jelani Nelson | Harvard University |  |
| Power and Politics in Today’s World | <PERSON> | Yale University |  |
| Death | <PERSON><PERSON> | Yale University |  |
| Game Theory | <PERSON> | Yale University |  |
| Predictably Irrational | Dan Ariely | Duke University |  |
| [How To Start A Startup](http://startupclass.samaltman.com/) | <PERSON> | Stanford University | ✅ |
| [Convex Optimization](https://see.stanford.edu) | Stephen P. Boyd | Stanford University |  |
| 6.0001 & 6.0002 | Eric Grimson | Massachusetts Institute of Technology | ✅ |
| 6.006 Introduction to Algorithms | Erik Demaine | Massachusetts Institute of Technology |  |
| 6.042J Mathematics for Computer Science | Tom Leighton | Massachusetts Institute of Technology |  |
| 15.S08 FinTech: Shaping the Financial World | Gary Gensler | Massachusetts Institute of Technology | ✅ |
| 18.S096 Topics in Mathematics w Applications in Finance | Peter Kempthorne | Massachusetts Institute of Technology |  |
| 15.401 Finance Theory I | Andrew Lo | Massachusetts Institute of Technology |  |
| Linear Algebra | Gilbert Strang | Massachusetts Institute of Technology |  |
| [The Missing Semester of Your CS Education](https://missing.csail.mit.edu/) | Anish | Massachusetts Institute of Technology | ✅ |
| [CS 61A: Structure and Interpretation of Computer Programs](https://inst.eecs.berkeley.edu/~cs61a/sp21/) | Paul N. Hilfinger | University of California, Berkeley | ✅ |
| Introduction to Computer Systems | Brian Railing | Carnegie Mellon University |  |
| 大学计算机基础 | 周海芳 | 国防科技大学 |  |
| [计算机网络](https://www.bilibili.com/video/BV1JV411t7ow/) | 郑烇 | 中国科学技术大学 |  |
| 操作系统 | [蒋炎岩](https://jyywiki.cn) | 南京大学 |  |
| 生物演化 | 顾红雅 | 北京大学 |  |
| 中国古代史 | 阎步克 | 北京大学 |  |
| 影片赏析 | 戴锦华 | 北京大学 | ✅ |
| 人工智能实践：Tensorflow笔记 | 曹健 | 北京大学 | ✅ |
| 行为经济学 | 孟涓涓 | 北京大学 | ✅ |
| 会计学基础 | 罗炜 | 北京大学 | ✅ |
| 宏观经济学 | 唐遥 | 北京大学 | ✅ |
| 微观经济学供给与需求 | 王辉 | 北京大学 | ✅ |
| 宏观经济学二十五讲 | [徐高](http://www.xugaoecon.net/) | 北京大学 | ✅ |
| 金融经济学二十五讲 | [徐高](http://www.xugaoecon.net/) | 北京大学 | ✅ |
| 投资银行学 | 冯科 | 北京大学 |  |
| 实用Python程序设计 | 郭炜 | 北京大学 | ❎ |
| 区块链技术与应用 | 肖臻 | 北京大学 |  |
| 量子力学 | 田光善 | 北京大学 |  |
| 微积分 | 苏德矿 | 浙江大学 |  |
| C语言程序设计 | 翁恺 | 浙江大学 | ✅ |
| 零基础学Java语言 | 翁恺 | 浙江大学 |  |
| 面向对象程序设计——Java语言 | 翁恺 | 浙江大学 |  |
| 机器学习 | 胡浩基 | 浙江大学 | ❎ |
| 管理概论 | 邢以群 | 浙江大学 | ✅ |
| 心理学与生活 | 陈昌凯 | 南京大学 | ✅ |
| 机器学习初步 | 周志华 | 南京大学 | ✅ |
| 《新教伦理与资本主义精神》导读 | 郁喆隽 | 复旦大学 | ✅ |
| 《资治通鉴》导读 | 姜鹏 | 复旦大学 | ✅ |
| 《资治通鉴》导读 | 张国刚 | 清华大学 | ✅ |
| 经济学原理 | 钱颖一 | 清华大学 |  |
| 财务分析与决策 | 肖星 | 清华大学 |  |
| 文物精品与文化中国 | 彭林 | 清华大学 |  |
| 区块链和加密数字货币 | 罗玫 | 清华大学 | ✅ |
| 中国建筑史（上） | 王贵祥 & 吕舟 | 清华大学 | ✅ |
| 数据结构（上） | 邓俊辉 | 清华大学 | ✅ |
| 组合数学 | 马昱春 | 清华大学 |  |
| 西方哲学史 | 赵林 | 武汉大学 |  |
| 运动与减脂塑形 | 陈一冰 | 北京师范大学 | ✅ |
| 中国古代建筑艺术 | 柳肃 | 湖南大学 | ✅ |
| 大数据技术原理与应用 | 林子雨 | 厦门大学 | ✅ |
| 大数据与金融 | 彭俞超 | 中央财经大学 | ✅ |
| 管理心理学（上） | 祝小宁 | 电子科技大学 | ✅ |
| 机器学习 | 李宏毅 | 台湾大学 |  |
| 强化学习纲要 | 周博磊 | 香港中文大学 |  |
| [Introduction to Complexity](https://www.complexityexplorer.org/) | Melanie Mitchell | Santa Fe Institute |  |
| [Machine Learning](https://coursera.org/share/411c836a9d6ed85d426ef62d0548fdee) | Andrew Ng | Stanford University | ✅ |
| [Project Management: Tools, Approaches, Behavioural Skills](https://coursera.org/share/64442c936cd400b9cab47301e37a761f) | Tommaso Buganza | Politecnico di Milano | ✅ |
| Mathematics for Engineers | Jeffrey R. Chasnov | The Hong Kong University of Science and Technology |  |
| Fundamentals of Computing | John Greiner | Rice University |  |
| Algorithms | Tim Roughgarden | Stanford University |  |
| Competitive Strategy and Organization Design | Tobias Kretschmer | Ludwig-Maximilians-Universität München (LMU) |  |
| Decentralized Finance (DeFi): The Future of Finance | Cam Harvey | Duke University |  |
| Programming Languages | Dan Grossman | University of Washington |  |
| Algorithms | Kevin Wayne | Princeton University |  |
| Nand to Tetris | Shimon Schocken | Hebrew University of Jerusalem |  |
| [Learning How to Learn](https://coursera.org/share/9e8f28a65dcb46aaf084adacabe68bf1) | Barbara Oakley | McMaster University, University of California San Diego | ✅ |
| Financial Markets | Robert Shiller | Yale University |  |
| The Science of Well-Being | Laurie Santos | Yale University |  |
| Introduction to Psychology | Paul Bloom | Yale University |  |
| 史記 (Shi Ji) | 呂世浩 | 台湾大学 |  |
| 中國古代歷史與人物：秦始皇 (Qin Shi Huang) | 呂世浩 | 台湾大学 |  |
| 红楼梦 | 欧丽娟 | 台湾大学 |  |